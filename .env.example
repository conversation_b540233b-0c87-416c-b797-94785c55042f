# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/database_name

# Authentication
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://default:password@hostname:port

# Email Service Configuration (for password reset functionality)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password

# Google OAuth Configuration (if using Google authentication)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Frontend URL (for CORS and redirects)
CLIENT_URL=https://your-frontend-domain.com

# Deployment URL (will be automatically set by Vercel)
DEPLOYED_URL=https://your-backend.vercel.app

# Environment
NODE_ENV=production
