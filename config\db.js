const mongoose = require('mongoose');

// Global variable to cache the database connection
let cachedConnection = null;

const connectDB = async () => {
    // If we have a cached connection and it's ready, return it
    if (cachedConnection && mongoose.connection.readyState === 1) {
        console.log('Using cached MongoDB connection');
        return cachedConnection;
    }

    try {
        // Configure mongoose for serverless environment
        mongoose.set('bufferCommands', false);

        // Connect with optimized settings for serverless
        const connection = await mongoose.connect(process.env.MONGO_URI, {
            maxPoolSize: 10, // Maintain up to 10 socket connections
            serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
            socketTimeoutMS: 45000, // Close sockets after 45 seconds
        });

        console.log('MongoDB connected successfully');
        cachedConnection = connection;
        return connection;
    } catch (error) {
        console.error('MongoDB connection error:', error);
        // Don't exit process in serverless environment
        throw new Error(`Database connection failed: ${error.message}`);
    }
};

// Function to ensure database connection before operations
const ensureConnection = async () => {
    if (mongoose.connection.readyState !== 1) {
        await connectDB();
    }
};

module.exports = { connectDB, ensureConnection };