{"version": 2, "name": "cwa-backend", "builds": [{"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/", "dest": "/api/health"}, {"src": "/auth-callback", "dest": "/api/auth/callback"}, {"src": "/api-docs", "dest": "/api/docs"}], "functions": {"api/**/*.js": {"maxDuration": 30}}, "env": {"NODE_ENV": "production"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}]}