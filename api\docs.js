const swaggerUi = require("swagger-ui-express");
const swaggerSpecs = require("../config/swagger");

module.exports = (req, res) => {
  // Serve Swagger UI
  if (req.method === 'GET') {
    const html = swaggerUi.generateHTML(swaggerSpecs, { explorer: true });
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  } else {
    res.status(405).json({
      status: 'error',
      message: 'Method not allowed'
    });
  }
};
