const express = require("express");
const { ensureConnection } = require("../../config/db");
const userRoutes = require("../../routes/userRoutes");

const app = express();

// Middleware
app.use(express.json());

// Ensure database connection
app.use(async (req, res, next) => {
  try {
    await ensureConnection();
    next();
  } catch (error) {
    console.error("Database connection error:", error);
    res.status(500).json({
      status: "error",
      message: "Database connection failed"
    });
  }
});

// Use user routes with proper path handling
app.use("/api/users", userRoutes);

module.exports = app;
