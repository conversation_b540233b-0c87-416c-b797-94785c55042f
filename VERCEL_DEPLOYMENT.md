# Vercel Deployment Guide for CWA Backend

This guide explains how to deploy the Chinioti Wooden Art backend to Vercel's serverless platform.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install globally with `npm i -g vercel`
3. **Environment Variables**: Prepare your environment variables

## Required Environment Variables

Set these in your Vercel dashboard or using the CLI:

```bash
# Database
MONGO_URI=mongodb+srv://username:<EMAIL>/database

# Authentication
JWT_SECRET=your-super-secret-jwt-key

# Redis (Optional - for caching)
REDIS_URL=redis://default:password@hostname:port

# Email Service (for password reset)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Google OAuth (if using)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Frontend URL
CLIENT_URL=https://your-frontend-domain.com

# Deployment URL (will be set automatically by Vercel)
DEPLOYED_URL=https://your-backend.vercel.app
```

## Deployment Steps

### 1. Install Vercel CLI and Login
```bash
npm install -g vercel
vercel login
```

### 2. Deploy from Project Root
```bash
# From the project root directory
vercel

# For production deployment
vercel --prod
```

### 3. Set Environment Variables
```bash
# Set environment variables via CLI
vercel env add MONGO_URI
vercel env add JWT_SECRET
vercel env add REDIS_URL
# ... add all other required variables
```

Or set them in the Vercel dashboard:
1. Go to your project dashboard
2. Navigate to Settings > Environment Variables
3. Add all required variables

### 4. Redeploy with Environment Variables
```bash
vercel --prod
```

## API Endpoints

After deployment, your API will be available at:

- **Health Check**: `https://your-app.vercel.app/api/health`
- **API Documentation**: `https://your-app.vercel.app/api/docs`
- **Authentication**: `https://your-app.vercel.app/api/auth/*`
- **Products**: `https://your-app.vercel.app/api/products/*`
- **Orders**: `https://your-app.vercel.app/api/orders/*`
- **Users**: `https://your-app.vercel.app/api/users/*`
- **Contact**: `https://your-app.vercel.app/api/contact/*`
- **Images**: `https://your-app.vercel.app/api/images/*`
- **Video Blogs**: `https://your-app.vercel.app/api/video-blogs/*`

## Testing the Deployment

### 1. Health Check
```bash
curl https://your-app.vercel.app/api/health
```

Expected response:
```json
{
  "status": "success",
  "message": "Chinioti Wooden Art API is healthy and running",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "services": {
    "database": {
      "status": "connected"
    },
    "redis": {
      "status": "connected"
    }
  }
}
```

### 2. Test API Endpoints
```bash
# Test product listing
curl https://your-app.vercel.app/api/products

# Test user registration
curl -X POST https://your-app.vercel.app/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123","phone":"**********"}'
```

## Troubleshooting

### Common Issues

1. **Database Connection Timeout**
   - Ensure MongoDB Atlas allows connections from all IPs (0.0.0.0/0)
   - Check if MONGO_URI is correctly set

2. **Function Timeout**
   - Vercel functions have a 30-second timeout limit
   - Optimize database queries and reduce response times

3. **Environment Variables Not Loading**
   - Redeploy after setting environment variables
   - Check variable names match exactly

4. **CORS Issues**
   - Update CLIENT_URL to match your frontend domain
   - Ensure CORS settings in the code allow your frontend origin

### Monitoring

- **Vercel Dashboard**: Monitor function invocations and errors
- **Health Endpoint**: Regular health checks at `/api/health`
- **Logs**: View function logs in Vercel dashboard

## Performance Optimization

1. **Database Connection Pooling**: Already implemented with connection caching
2. **Redis Caching**: Configured for API response caching
3. **Function Optimization**: Each route is optimized for serverless execution

## Security Considerations

1. **Environment Variables**: Never commit secrets to version control
2. **CORS Configuration**: Restrict origins in production
3. **Rate Limiting**: Consider implementing rate limiting for production
4. **Input Validation**: All inputs are validated before processing

## Support

For deployment issues:
1. Check Vercel function logs
2. Test the health endpoint
3. Verify environment variables are set correctly
4. Ensure database connectivity from Vercel's IP ranges
